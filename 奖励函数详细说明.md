# 重返秘境 - 奖励函数详细说明

## 概览

奖励函数由**8个主要组件**构成，采用分阶段训练和多目标平衡的设计理念：

```python
r = (重复惩罚 + 终点奖励 + 距离奖励 + 宝藏奖励 + 撞墙惩罚 + buff奖励 + 每步惩罚) × 0.1
```

## 1. 重复访问惩罚 - "防止原地打转"

### 目的与机制
防止智能体在同一区域反复徘徊，鼓励探索新区域。游戏环境维护记忆地图，记录访问次数。

### 分阶段设计

#### 训练前期（ratio < 0.5）：简单惩罚
```python
r -= max(obs['memory_map'][25,25] - args.repeat_step_thre, 0)
```

**计算步骤：**
1. 获取当前位置访问次数：`memory_map[25,25]`
2. 减去阈值0.4：`访问次数 - 0.4`
3. 取正值作为惩罚：`max(结果, 0)`

**示例：**
```
当前位置访问0.6次：
惩罚 = max(0.6 - 0.4, 0) = 0.2
```

#### 训练后期（ratio ≥ 0.5）：复杂惩罚
```python
r -= (args.repeat_punish * np.maximum(
  obs['memory_map'][23:28,23:28]-args.repeat_step_thre, 0)).sum()
```

**权重矩阵：**
```
[0.0, 0.0, 0.0, 0.0, 0.0]
[0.0, 0.5, 0.8, 0.5, 0.0]
[0.0, 0.8, 1.0, 0.8, 0.0]  ← 中心权重最高
[0.0, 0.5, 0.8, 0.5, 0.0]
[0.0, 0.0, 0.0, 0.0, 0.0]
```

**计算步骤：**
1. 提取5×5区域访问情况
2. 每位置减去阈值0.4，取正值
3. 乘以权重矩阵
4. 求和得到总惩罚

## 2. 终点到达奖励 - "任务完成的终极目标"

### 目的与机制
游戏核心目标，给予最高正向激励，同时检查任务完成质量。

### 计算公式
```python
if terminated:
  r += 150  # 基础奖励
  r -= (obs['treasure_flags'] * self.treasure_reward_coef).sum() * 100  # 遗漏宝藏
  r -= obs['buff_flag'] * args.forget_buff_punish  # 遗漏buff
```

**计算示例：**
```python
# 到达终点时状态
treasure_flags = [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]  # 遗漏2个宝藏
buff_flag = 1  # 未获得buff

# 计算过程
base_reward = 150
treasure_penalty = 2 * 100 = 200
buff_penalty = 1 * 5 = 5
final_reward = 150 - 200 - 5 = -55
```

## 3. 距离奖励 - "指引前进方向的罗盘"

### 目的与机制
实时引导智能体朝正确方向移动，包括向终点和向宝藏移动。

### 向终点移动奖励
```python
dist_reward_coef = 5.0 if self.use_flash else 1.0  # 闪现系数更高
delta_end_distance = self._obs['end_pos']['grid_distance'] - obs['end_pos']['grid_distance']
r += delta_end_distance * dist_reward_coef
```

**计算示例：**
```python
# 普通移动向终点靠近
previous_distance = 10.5
current_distance = 9.2
delta = 10.5 - 9.2 = 1.3

# 普通移动：reward = 1.3 * 1.0 = 1.3
# 闪现移动：reward = 1.3 * 5.0 = 6.5
```

### 向宝藏移动奖励
```python
if sum(self._obs['treasure_flags']):  # 有未收集宝藏
  dist_treasure = np.max((self._obs['treasure_grid_distance'] - 
                          self.obs['treasure_grid_distance'])[self._obs['treasure_flags']])
  r += dist_treasure * dist_reward_coef
```

## 4. 宝藏收集奖励 - "寻宝的动力源泉"

### 目的与机制
激励智能体主动收集地图上的宝藏，使用异或操作检测新收集的宝藏。

### 计算公式
```python
if not terminated and score == 100:  # 收集到宝藏
  r += 100 * (self.treasure_reward_coef * 
               (self._obs['treasure_flags'] ^ obs['treasure_flags'])).sum()
```

**计算示例：**
```python
# 收集宝藏前后状态
previous_flags = [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
current_flags =  [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]

# 异或找出变化
treasure_change = previous_flags ^ current_flags  # [0, 1, 0, 0, ...]
reward = 100 * 1.0 * 1 = 100  # 收集1个宝藏
```

## 5. 撞墙惩罚 - "避免无效行动的教训"

### 目的与机制
惩罚无效移动，通过比较实际移动距离与期望距离判断撞墙。

### 撞墙检测
```python
def _check_hit_wall(self):
  _abs_pos, abs_pos = [x['norm_pos'] * 64000 for x in [self._obs, self.obs]]
  dist = np.linalg.norm(_abs_pos - abs_pos)  # 实际移动距离
  
  # 期望距离：普通660-711码，buff加速900-1100码，闪现7900-8100码
  if dist < expected_distance:
    return True
```

### 惩罚计算
```python
if hit_wall:
  r -= 10.0 if self.use_flash else 1.0  # 闪现撞墙惩罚更重
```

## 6. Buff相关奖励 - "战略资源的获取"

### 目的与机制
鼓励获得加速buff（提供50步移速加成），检测buff状态变化。

### 计算公式
```python
if self._obs['buff_flag'] - obs['buff_flag'] == 1:  # 刚获得buff
  r += 5.0
```

## 7. 每步惩罚 - "时间压力的驱动"

### 目的与机制
训练后期增加时间压力，促使寻找更高效策略。

### 计算公式
```python
if ratio > 0.5 or args.load_model_id is not None:  # 训练后期或加载模型
  r -= 0.2  # 每步固定惩罚
```

## 8. 全局缩放 - "数值稳定保障"

### 目的与机制
控制奖励范围，确保PPO算法训练稳定性。

### 计算公式
```python
r *= 0.1  # 所有奖励统一缩放
```

## 奖励数值范围总结

| 奖励类型 | 原始范围 | 缩放后范围 | 备注 |
|----------|----------|------------|------|
| **终点到达** | +150 | +15.0 | 最高正奖励 |
| **宝藏收集** | +100×系数 | +10.0×系数 | 每个宝藏 |
| **遗漏宝藏** | -100×系数×13 | -130.0 | 最大负惩罚 |
| **距离奖励** | ±1.0~5.0×变化 | ±0.1~0.5×变化 | 取决于移动方式 |
| **闪现撞墙** | -10.0 | -1.0 | 严重惩罚 |
| **普通撞墙** | -1.0 | -0.1 | 轻微惩罚 |
| **获得buff** | +5.0 | +0.5 | 中等奖励 |
| **遗漏buff** | -5.0 | -0.5 | 中等惩罚 |
| **重复访问** | -权重×超出值 | -0.01~0.1 | 取决于访问次数 |
| **每步惩罚** | -0.2 | -0.02 | 训练后期 |

## 设计特点

1. **分阶段训练**：前期简单规则，后期复杂规则
2. **多目标平衡**：主要目标奖励远大于行为塑造惩罚
3. **动态调整**：根据训练进度和移动方式调整系数
4. **空间感知**：重复惩罚考虑周围区域
5. **数值稳定**：全局缩放控制范围，有利于收敛

## 完整计算示例

```python
# 某一步的各项奖励计算
repeat_penalty = -0.5      # 重复访问惩罚
distance_reward = +2.0     # 向目标移动奖励
treasure_reward = +100.0   # 收集宝藏奖励
wall_penalty = -1.0        # 撞墙惩罚
step_penalty = -0.2        # 每步惩罚

# 求和
total_reward = -0.5 + 2.0 + 100.0 - 1.0 - 0.2 = 100.3

# 全局缩放
final_reward = 100.3 * 0.1 = 10.03
```

这个奖励函数设计精巧，既保证任务目标明确性，又通过细致的行为塑造引导智能体学习高效导航策略。
