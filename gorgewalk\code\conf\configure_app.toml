[app]

# === === === === === === === === === === === === === === === === === === === ===
# 下面的配置使用者可以按照需要修改
# === === === === === === === === === === === === === === === === === === === ===

# 日志相关配置
log_dir = "/data/projects/gorge_walk/log"
level = "INFO"

# 采用的算法
algo = "diy"

# learner上reverb样本的输入维度, 注意不同的算法维度不一样, 比如sarsa的维度是6
sample_dim = 6

# learner执行while True循环的进行训练，设置休息时间以便设置样本生产消耗比
learner_train_by_while_true = true
learner_train_sleep_seconds = 0.0001

# 下面是replay buffer的相关配置
replay_buffer_capacity = 4096
preload_ratio = 5  # 样本池中达到1/preload_ratio时开始训练

# learner训练批处理大小限制
train_batch_size = 256
# 样本消耗/生成采样比
production_consume_ratio = 10  # 3 ?

# 评估模式模型文件夹路径和ID

# === === === === === === === === === === === === === === === === === === === ===
# 下面的配置, 由框架使用, 不建议使用者修改
# === === === === === === === === === === === === === === === === === === === ===

# 进程启动前已经由框架的脚本自动化修改完成
app = "gorge_walk"
self_play = false
set_name = "set1"
self_play_set_name = "set2"
selfplay_app_conf = "conf/nature_app_gorge_walk.yaml"
noselfplay_app_conf = "conf/nature_app_gorge_walk.yaml"
algo_conf = "conf/nature_algo_gorge_walk.yaml"
rainbow_env_name = "gorge_walk_dev"

# 主要区分训练和评估模式
run_mode = "train"
# run_mode = "eval"

# 使用的强化学习框架, 包括tensorflow_simple, tensorflow_complex, tensorrt, pytorch等, 默认是tensorflow_simple
use_which_deep_learning_framework = "pytorch"

# 预测是放在actor远程还是aisrv本地, 小规模场景建议是aisrv本地local模式, 大规模场景和小规模场景都可以使用的actor远程remote模式
predict_local_or_remote = "local"

# 设置的actor, learner地址
actor_addrs = { train_one = ["127.0.0.1:8888"] }
learner_addrs = { train_one = ["127.0.0.1:9999"] }

# 是否同步将model文件上传到cos
need_to_sync = false

# 接入采用标准化模式
framework_integration_patterns = "standard"

# 采用接入KaiwuEnv方式
aisrv_framework = "kaiwu_env"

# 采用的wrapper形式, 包括remote, local, none
wrapper_type = "local"

# 在模型文件保存时, 需要保存的文件目录, 多个目录请按照逗号分割, 并且是以项目根目录开始看的
copy_dir = "conf,dynamic_programming,monte_carlo,sarsa,q_learning,diy"

# 训练间隔多少步输出model文件, 该值需要根据实际情况调整, 设置过小则会导致落模型耗时长, 影响时延; 如果设置过大则导致learner/actor之间model同步间隔长
dump_model_freq = 10000

# model文件FIFO的个数, 如果是采用最新的模型则设置为1; 需要采用历史模型则该值设置为需要的比如50, FIFO模式
modelpool_max_save_model_count = 1

# actor预测批处理大小
predict_batch_size = 1
eval_model_dir = "/data/projects/gorge_walk/ckpt"
eval_model_id = "1249984"
