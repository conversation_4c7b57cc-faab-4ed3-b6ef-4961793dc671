import numpy as np

# 超参数配置
class Args:
    version = "1.2.2_dppo_pretrain_1.2.1_955"  # 模型版本

    # 算法特定参数
    total_timesteps = int(1e7)  # 实验的总时间步数
    # total_timesteps = int(8000*2)  # 实验的总时间步数
    learning_rate = 1e-5  # 优化器的学习率，这个可以在稳定之后下降，比如除以十
    # group_lr = [2.5e-4, 2.5e-5]  # 按照步数比例调整学习率(在不启动退火的前提下)
    num_envs = 10  # 并行环境数量
    # num_envs = 4  # 并行环境数量
    num_steps = 256  # 每个环境在每次策略rollout中运行的步数，这个最好大于一个episode的长度，设置成512或者1024
    # num_steps = 32  # 每个环境在每次策略rollout中运行的步数，这个最好大于一个episode的长度，设置成512或者1024
    anneal_lr = False  # 是否退火学习率
    gamma = 0.999  # 折扣因子gamma，这个要再高一些 0.999左右，也可以是1
    gae_lambda = 0.95  # 广义优势估计的lambda参数
    minibatch_size = 64  # 小批次大小 (n_sample*n_steps)
    update_epochs = 2  # 更新策略的K个轮次
    norm_adv = False  # 优势归一化，可能不需要
    clip_coef = 0.2  # 代理裁剪系数
    clip_vloss = False  # 是否对价值函数使用裁剪损失，如论文所述
    ent_coef = 0  # 熵系数，一般设置成1e-4，在训练最优模型的时候设置成0
    # group_ent_coef = [1e-2, 1e-4]  # 按比例调整熵系数
    vf_coef = 0.5  # 价值函数系数
    max_grad_norm = 0.5  # 梯度裁剪的最大范数
    target_kl = None  # 目标KL散度阈值

    # 运行时填充
    batch_size = 0  # 批次大小（运行时计算）
    num_iterations = 0  # 迭代次数（运行时计算）

    ### 网络配置 ###
    observation_img_shape = (4, 51, 51)
    observation_vec_shape = (31,)
    # 图像 + 向量 + 闪现可用性 (掩码)
    obs_dim = np.prod(observation_img_shape) + observation_vec_shape[0] + 1
    ### 环境配置 ###
    # n_treasure = ['norm', 13, 'norm']
    n_treasure = "uniform"
    norm_sigma = 3.0
    ### 奖励配置 ###
    # 距离奖励
    dist_reward_coef = 1.0
    flash_dist_reward_coef = 5.0
    # 重复行走惩罚
    repeat_punish = np.array([
        [0, 0, 0, 0, 0],
        [0, 0.5, 0.8, 0.5, 0],
        [0, 0.8, 1.0, 0.8, 0],
        [0, 0.5, 0.8, 0.5, 0],
        [0, 0, 0, 0, 0],
    ], np.float32),
    # 宝藏相关
    treasure_miss_reset_episode = 100
    # 重复步数阈值
    repeat_step_thre = 0.4
    # 撞墙惩罚
    walk_hit_wall_punish = 1.0
    flash_hit_wall_punish = 10.0
    # buff相关
    get_buff_reward = 5.0
    forget_buff_punish = 5.0
    # 全局系数
    reward_global_coef = 1 / 10
    # 每步惩罚 (仅在ratio > 0.5时考虑)
    each_step_punish = 0.2
    # 动态宝藏奖励
    dynamic_treasure_reward = False
    # 随机起始位置
    random_start_position_ratio = 0.0
    ### 模型加载 ###
    load_model_id = "955"

args = Args()

# 配置类
# 配置，包含维度设置，算法参数设置，文件的最后一些配置是开悟平台使用不要改动
class Config:

    # learner上reverb样本的输入维度，注意不同的算法维度不一样
    # 比如示例代码中dqn的维度是21624, target_dqn的维度是21624
    # **注意**，此项必须正确配置，应该与definition.py中的NumpyData2SampleData函数数据对齐，否则可能报样本维度错误
    # obs: Image: (4, 51, 51), Vector: (31,), Flash: (1,) 4*51*51+31+1 = 10436
    # action: (1,)
    # reward: (1,)
    # done: (1,)
    # next_obs: (10436,)
    # next_done: (1,)
    # 单个样本维度: num_steps * (obs, action, reward, done, logprob) + next_obs + next_done
    SAMPLE_DIM = args.num_steps * (args.obs_dim + 4) + args.obs_dim + 1

    # 观察空间的维度
    # observation的维度，用户设计了自己的特征之后应该设置正确的维度
    DIM_OF_OBSERVATION = 0

    # 移动动作方向的维度
    DIM_OF_ACTION_DIRECTION = 8

    # 闪现动作方向的维度
    DIM_OF_TALENT = 8

    # 关于开悟平台使用的配置，以下配置可以忽略，不需要改动
    SUB_ACTION_MASK_SHAPE = 0
    LSTM_HIDDEN_SHAPE = 0
    LSTM_CELL_SHAPE = 0
    OBSERVATION_SHAPE = 45000
    LEGAL_ACTION_SHAPE = 2
