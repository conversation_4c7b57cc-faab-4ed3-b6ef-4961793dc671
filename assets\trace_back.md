# secret realm
## learner
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 21> in __init__ +++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/common/algorithms/model_wrapper_common.py, line 46> in create_standard_model_wrapper 
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/learner/on_policy_trainer.py, line 874> in before_run +++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/learner/on_policy_trainer.py, line 1476> in loop ++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/learner/learner.py, line 144> in train_loop +++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/learner/learner.py, line 167> in main +++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 108> in run +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/train_test.py, line 99> in train ++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/train_test.py, line 205> in <module> ++++++++++++++++++++++++++++++++++++
???
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 21> in __init__ ---------------------------------------------
```

## aisvr
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 21> in __init__ +++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/common/algorithms/model_wrapper_common.py, line 39> in create_standard_model_wrapper 
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/aisrv/actor_proxy_local.py, line 532> in before_run +++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/aisrv/actor_proxy_local.py, line 935> in run ++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/aisrv/async_policy.py, line 219> in __init__ ++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/aisrv/aisrv_server_standard.py, line 597> in before_run +
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/aisrv/aisrv_server_standard.py, line 446> in run ++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/kaiwudrl/server/aisrv/aisrv.py, line 154> in main +++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 108> in run +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/train_test.py, line 99> in train ++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/back_to_the_realm/train_test.py, line 205> in <module> ++++++++++++++++++++++++++++++++++++
???
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 21> in __init__ ---------------------------------------------
```

# gorgewalk
## learner
### init agent
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 23> in __init__ +++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/model_wrapper_common.py, line 46> in create_standard_model_wrapper 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/on_policy_trainer.py, line 873> in before_run ++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/on_policy_trainer.py, line 1472> in loop +++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/learner.py, line 149> in train_loop ++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/learner.py, line 172> in main ++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 108> in run +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/train_test.py, line 111> in train ++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/train_test.py, line 231> in <module> +++++++++++++++++++++++++++++++++++++++++++
init Agent
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 23> in __init__ ---------------------------------------------
```
### save_model
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 75> in save_model +++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 219> in save_param_detail 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 178> in save_param 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/interface/base_agent_kaiwudrl_local.py, line 114> in wrapper ++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 162> in save_param_by_source 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/on_policy_trainer.py, line 961> in before_run ++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/on_policy_trainer.py, line 1472> in loop +++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/learner.py, line 149> in train_loop ++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/learner/learner.py, line 172> in main ++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 108> in run +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/train_test.py, line 111> in train ++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/train_test.py, line 231> in <module> +++++++++++++++++++++++++++++++++++++++++++
Save model path='/data/ckpt/gorge_walk_diy' id=0
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 75> in save_model -------------------------------------------
```
## aisvr
### init agent
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 23> in __init__ +++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/model_wrapper_common.py, line 39> in create_standard_model_wrapper 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/actor_proxy_local.py, line 532> in before_run ++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/actor_proxy_local.py, line 935> in run +++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/async_policy.py, line 219> in __init__ +++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/aisrv_server_standard.py, line 597> in before_run ++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/aisrv_server_standard.py, line 446> in run +++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/aisrv.py, line 159> in main ++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 108> in run +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 314> in _bootstrap ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 71> in _launch +++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/popen_fork.py, line 19> in __init__ ++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 281> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/context.py, line 224> in _Popen ++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/multiprocessing/process.py, line 121> in start +++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/train_test.py, line 111> in train ++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/train_test.py, line 231> in <module> +++++++++++++++++++++++++++++++++++++++++++
init Agent
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 23> in __init__ ---------------------------------------------
```
### init agent2
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 23> in __init__ +++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/model_wrapper_common.py, line 39> in create_standard_model_wrapper 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 373> in before_run +++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 466> in run ++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1045> in _bootstrap_inner +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1002> in _bootstrap +++++++++++++++++++++++++++++++++++++++++++++
init Agent
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 23> in __init__ ---------------------------------------------
```
### save model
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 75> in save_model +++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 219> in save_param_detail 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 178> in save_param 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/interface/base_agent_kaiwudrl_local.py, line 114> in wrapper ++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 162> in save_param_by_source 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 387> in before_run +++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 466> in run ++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1045> in _bootstrap_inner +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1002> in _bootstrap +++++++++++++++++++++++++++++++++++++++++++++
Save model path='/data/ckpt/gorge_walk_diy' id=0
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 75> in save_model -------------------------------------------
```
### workflow
```python
[DEBUG] FROM </workspace/code/diy/train_workflow.py, line 15> in workflow ++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 432> in workflow +++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 476> in run ++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1045> in _bootstrap_inner +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1002> in _bootstrap +++++++++++++++++++++++++++++++++++++++++++++
DEBUG
[DEBUG] END  </workspace/code/diy/train_workflow.py, line 15> in workflow ----------------------------------------------
```
### learn
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 70> in learn ++++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/interface/base_agent_kaiwudrl_local.py, line 36> in wrapper +++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 281> in train_local 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 323> in train_local ++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/interface/base_agent_kaiwudrl_local.py, line 39> in wrapper +++++++++++
[DEBUG] IN   </workspace/code/diy/train_workflow.py, line 15> in workflow ++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 432> in workflow +++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 476> in run ++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1045> in _bootstrap_inner +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1002> in _bootstrap +++++++++++++++++++++++++++++++++++++++++++++
agent.learn
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 70> in learn ------------------------------------------------
```
### save 10000
```python
[DEBUG] FROM </workspace/code/diy/algorithm/agent.py, line 75> in save_model +++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 219> in save_param_detail 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 178> in save_param 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 317> in save_param +++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/interface/base_agent_kaiwudrl_local.py, line 114> in wrapper ++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 108> in after_train 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/common/algorithms/standard_model_wrapper_pytorch.py, line 284> in train_local 
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 323> in train_local ++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/interface/base_agent_kaiwudrl_local.py, line 39> in wrapper +++++++++++
[DEBUG] IN   </workspace/code/diy/train_workflow.py, line 18> in workflow ++++++++++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 432> in workflow +++++++
[DEBUG] IN   </data/projects/gorge_walk/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py, line 476> in run ++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1045> in _bootstrap_inner +++++++++++++++++++++++++++++++++++++++
[DEBUG] IN   </usr/lib64/python3.11/threading.py, line 1002> in _bootstrap +++++++++++++++++++++++++++++++++++++++++++++
Save model path='/data/ckpt/gorge_walk_diy' id=10000
[DEBUG] END  </workspace/code/diy/algorithm/agent.py, line 75> in save_model -------------------------------------------
```